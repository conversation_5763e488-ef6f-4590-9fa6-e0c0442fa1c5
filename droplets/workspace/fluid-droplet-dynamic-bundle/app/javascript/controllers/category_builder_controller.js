import { Controller } from "@hotwired/stimulus"

// Category Builder Controller - Manages bundle categories and product assignment
export default class extends Controller {
  static targets = [
    "categoriesContainer",
    "categoriesList",
    "addCategoryButton",
    "categoryTemplate"
  ]

  static values = {
    categories: { type: Array, default: [] }
  }

  connect() {
    console.log("📂 Category Builder Controller connected!")

    // Make controller globally accessible for modal callbacks
    window.categoryBuilderController = this

    // Initialize categories from session data
    this.initializeCategories()

    // Set up global drag & drop event listeners
    this.setupGlobalDragListeners()
  }

  disconnect() {
    // Clean up global event listeners
    this.cleanupGlobalDragListeners()

    // Clean up global reference
    if (window.categoryBuilderController === this) {
      window.categoryBuilderController = null
    }
  }

  // Initialize categories from existing data
  initializeCategories() {
    console.log('🚀 Initializing categories...')

    // Render categories (table if categories exist, empty state if not)
    this.renderCategoriesView()

    // Initialize button state
    this.updateButtonState()
  }

  // REMOVED: fetchCategories method - no longer needed since button was removed

  // Show categories selection modal
  showCategoriesModal(categories) {
    // Create modal HTML
    const modalHtml = this.createCategoriesModalHtml(categories)

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml)

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('categoriesSelectionModal'))
    modal.show()

    // Clean up modal when hidden
    document.getElementById('categoriesSelectionModal').addEventListener('hidden.bs.modal', function() {
      this.remove()
    })
  }

  // Create categories selection modal HTML
  createCategoriesModalHtml(categories) {
    const categoriesHtml = categories.map(category => `
      <div class="col-md-6 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <div class="form-check">
              <input class="form-check-input"
                     type="checkbox"
                     value="${category.id}"
                     id="category-${category.id}"
                     data-category-name="${category.name}"
                     data-category-description="${category.description || ''}">
              <label class="form-check-label" for="category-${category.id}">
                <h6 class="card-title mb-1">${category.name}</h6>
                ${category.description ? `<p class="card-text text-muted small mb-0">${category.description}</p>` : ''}
              </label>
            </div>
          </div>
        </div>
      </div>
    `).join('')

    return `
      <div class="modal fade" id="categoriesSelectionModal" tabindex="-1" aria-labelledby="categoriesSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="categoriesSelectionModalLabel">Select Categories from Fluid Store</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
              <p class="text-muted mb-3">Select categories from your Fluid store to add to your bundle:</p>

              <!-- Auto-load products option -->
              <div class="alert alert-info mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="autoLoadProducts" checked>
                  <label class="form-check-label" for="autoLoadProducts">
                    <strong>Auto-load products</strong> - Automatically fetch and add products from selected categories
                  </label>
                </div>
              </div>

              <div class="row">
                ${categoriesHtml}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button"
                      class="btn btn-primary"
                      onclick="categoryBuilderController.addSelectedCategories()">
                Add Selected Categories
              </button>
            </div>
          </div>
        </div>
      </div>
    `
  }

  // Add selected categories from modal
  async addSelectedCategories() {
    const modal = document.getElementById('categoriesSelectionModal')
    const selectedCheckboxes = modal.querySelectorAll('input[type="checkbox"]:checked')
    const autoLoadProducts = modal.querySelector('#autoLoadProducts').checked

    // Filter out the autoLoadProducts checkbox
    const categoryCheckboxes = Array.from(selectedCheckboxes).filter(cb => cb.id !== 'autoLoadProducts')

    if (categoryCheckboxes.length === 0) {
      alert('Please select at least one category.')
      return
    }

    // Close modal first
    const bootstrapModal = bootstrap.Modal.getInstance(modal)
    if (bootstrapModal) {
      bootstrapModal.hide()
    }

    let addedCount = 0
    const categoriesToProcess = []

    // First, add all categories
    categoryCheckboxes.forEach(checkbox => {
      // Check if we've reached the maximum of 12 categories
      if (this.categoriesValue.length >= 12) {
        alert('Maximum of 12 categories allowed for bundle configuration.')
        return
      }

      const categoryId = `cat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const categoryName = checkbox.dataset.categoryName
      const categoryDescription = checkbox.dataset.categoryDescription
      const fluidCategoryId = checkbox.value

      const newCategory = {
        categoryId: categoryId,
        categoryName: categoryName,
        displayOrder: this.categoriesValue.length,
        selectionQuantity: 1,
        products: [],
        fluidCategoryId: fluidCategoryId, // Store original Fluid category ID
        description: categoryDescription
      }

      this.categoriesValue.push(newCategory)
      addedCount++

      // Store for product loading if needed
      if (autoLoadProducts) {
        categoriesToProcess.push({ categoryId, fluidCategoryId, categoryName })
      }
    })

    // Re-render the categories view to show the table
    this.renderCategoriesView()

    // Update button state
    this.updateButtonState()
    this.updateSessionData()

    console.log(`✅ Added ${addedCount} categories from Fluid store`)

    // Auto-load products if requested
    if (autoLoadProducts && categoriesToProcess.length > 0) {
      console.log(`🔄 Auto-loading products for ${categoriesToProcess.length} categories...`)
      await this.loadProductsForCategories(categoriesToProcess)
    }
  }

  // Load products for multiple categories
  async loadProductsForCategories(categories) {
    for (const { categoryId, fluidCategoryId, categoryName } of categories) {
      try {
        console.log(`🔄 Loading products for category: ${categoryName} (${fluidCategoryId})`)

        const response = await fetch(`/admin/categories/products/${fluidCategoryId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.products && data.products.length > 0) {
          console.log(`✅ Loaded ${data.products.length} products for ${categoryName}`)

          // Add products to the category
          const category = this.findCategory(categoryId)
          if (category) {
            data.products.forEach((product, index) => {
              const newProduct = {
                productId: product.id,
                productName: product.name,
                productSku: product.sku || 'N/A',
                productPrice: product.price || 0,
                displayOrder: index,
                isDefault: index === 0 // First product is default
              }
              category.products.push(newProduct)
            })

            // Re-render the categories view to show updated products count
            this.renderCategoriesView()
          }
        } else {
          console.warn(`⚠️ No products found for category: ${categoryName}`)
        }
      } catch (error) {
        console.error(`❌ Error loading products for category ${categoryName}:`, error)
      }
    }

    // Update session data after all products are loaded
    this.updateSessionData()
    console.log(`✅ Finished auto-loading products for all categories`)
  }

  // Add new category
  addCategory() {
    console.log('🆕 🆕 🆕 ADD CATEGORY BUTTON CLICKED! 🆕 🆕 🆕')
    console.log('🆕 Adding new category...')
    console.log('📊 Current categories before add:', this.categoriesValue.length)
    console.log('📊 Current categories array:', this.categoriesValue)

    // Check if we've reached the maximum of 12 categories
    if (this.categoriesValue.length >= 12) {
      alert('Maximum of 12 categories allowed for bundle configuration.')
      return
    }

    const categoryId = `cat-${Date.now()}`
    const newCategory = {
      categoryId: categoryId,
      categoryName: '',
      displayOrder: this.categoriesValue.length,
      selectionQuantity: 1,
      products: []
    }

    console.log('🆕 Created new category:', newCategory)

    // ✅ Stimulus Values require reassignment, not push
    this.categoriesValue = [...this.categoriesValue, newCategory]

    console.log('📊 Categories after reassignment:', this.categoriesValue.length)
    console.log('📊 All categories:', this.categoriesValue.map(c => ({ id: c.categoryId, name: c.categoryName })))

    // Re-render the categories view to show the table
    this.renderCategoriesView()

    // Update button state
    this.updateButtonState()

    this.updateSessionData()
  }

  // Render category form (inline editing)
  renderCategoryForm(category) {
    console.log('📝 Rendering category form for:', category.categoryId)

    const container = this.categoriesListTarget
    if (!container) {
      console.error('❌ Categories container not found')
      return
    }

    // Create form HTML
    const formHtml = this.createCategoryFormHtml(category)
    container.innerHTML = formHtml

    // Focus on name input
    setTimeout(() => {
      const nameInput = container.querySelector('input[name="categoryName"]')
      if (nameInput) {
        nameInput.focus()
      }
    }, 100)

    console.log('✅ Category form rendered')
  }

  // Hide Add Category button
  hideAddCategoryButton() {
    const button = this.addCategoryButtonTarget
    if (button) {
      button.style.display = 'none'
    }
  }

  // Show Add Category button
  showAddCategoryButton() {
    const button = this.addCategoryButtonTarget
    if (button) {
      button.style.display = 'block'
    }
  }

  // Create HTML for category form
  createCategoryFormHtml(category) {
    const productsCount = category.products.length
    const productsText = productsCount === 0
      ? 'No products added yet'
      : productsCount === 1
        ? '1 product added'
        : `${productsCount} products added`

    return `
      <div class="card mb-3" data-category-id="${category.categoryId}">
        <!-- Category Header - Inline Form -->
        <div class="card-header bg-light">
          <div class="row align-items-center">
            <div class="col">
              <input type="text"
                     name="categoryName"
                     value="${category.categoryName}"
                     placeholder="Category Name (e.g., Protein Powders)"
                     class="form-control form-control-sm"
                     data-action="input->category-builder#updateCategoryName">
            </div>
            <div class="col-auto">
              <div class="input-group input-group-sm">
                <span class="input-group-text">Qty:</span>
                <input type="number"
                       name="selectionQuantity"
                       value="${category.selectionQuantity}"
                       min="1"
                       max="10"
                       class="form-control"
                       style="max-width: 70px;"
                       data-action="input->category-builder#updateSelectionQuantity">
              </div>
            </div>
          </div>
        </div>

        <!-- Products List -->
        <div class="card-body">
          <div class="products-container">
            <!-- Products List -->
            <div data-category-products>
              ${this.createProductsListHtml(category.products)}
            </div>

            <!-- Empty State -->
            <div class="text-center text-muted p-4 ${category.products.length > 0 ? 'd-none' : ''}" data-empty-state>
              <div class="mb-2" style="font-size: 2rem;">📦</div>
              <p class="mb-1">No products added yet</p>
              <small>Click "Add Products" to select products for this category</small>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-footer bg-light">
          <div class="d-flex justify-content-between align-items-center">
            <button type="button"
                    class="btn btn-primary btn-sm"
                    data-action="click->category-builder#openProductModal"
                    data-category-id="${category.categoryId}">
              + Add Products
            </button>

          </div>
        </div>
      </div>
    `
  }

  // Create HTML for products list
  createProductsListHtml(products) {
    if (!products || products.length === 0) {
      return ''
    }

    return products.map((product, index) =>
      this.createProductInCategoryHtml(product, index)
    ).join('')
  }

  // Render categories view (table, inline form, or empty state)
  renderCategoriesView() {
    console.log('🎨 Rendering categories view...')
    console.log('📊 Categories to render:', this.categoriesValue.length)

    const container = this.categoriesListTarget
    if (!container) {
      console.error('❌ Categories container not found')
      return
    }

    if (this.categoriesValue.length === 0) {
      // Show empty state
      console.log('🎨 → Showing empty state')
      container.innerHTML = this.createEmptyStateHtml()
      this.showAddCategoryButton()
    } else if (this.hasIncompleteCategoryWithoutProducts()) {
      // Show inline form for category without products
      const incompleteCategory = this.getFirstIncompleteCategoryWithoutProducts()
      console.log('🎨 → Showing inline form for:', incompleteCategory.categoryName, 'Products:', incompleteCategory.products.length)
      container.innerHTML = this.createInlineFormHtml(incompleteCategory)
      this.hideAddCategoryButton()

      // Focus on name input after render
      setTimeout(() => {
        const nameInput = container.querySelector('input[name="categoryName"]')
        if (nameInput) {
          nameInput.focus()
        }
      }, 100)
    } else {
      // Show categories table (all categories have products)
      console.log('🎨 → Showing categories table')
      console.log('🎨 → All categories have products:', this.categoriesValue.map(c => ({ name: c.categoryName, products: c.products.length })))
      container.innerHTML = this.createCategoriesTableHtml()
      this.showAddCategoryButton()
    }

    console.log('✅ Categories view rendered')
  }

  // Render all categories as table (legacy method for compatibility)
  renderCategories() {
    this.renderCategoriesView()
  }

  // Create HTML for empty state
  createEmptyStateHtml() {
    return `
      <div class="text-center py-5">
        <div class="mb-3" style="font-size: 3rem;">📦</div>
        <h5 class="text-muted">No categories created yet</h5>
        <p class="text-muted">Click "Add Category" below to get started</p>
      </div>
    `
  }

  // Create HTML for inline form (name, qty, add products)
  createInlineFormHtml(category) {
    return `
      <div class="card">
        <div class="card-header bg-light">
          <h6 class="mb-0">📂 Configure Category</h6>
        </div>
        <div class="card-body">
          <div class="row align-items-end">
            <div class="col-md-5">
              <label class="form-label">Category Name</label>
              <input type="text"
                     name="categoryName"
                     value="${category.categoryName || ''}"
                     placeholder="e.g., Protein Powders"
                     class="form-control"
                     data-action="input->category-builder#updateCategoryName">
            </div>
            <div class="col-md-3">
              <label class="form-label">Required Quantity</label>
              <input type="number"
                     name="selectionQuantity"
                     value="${category.selectionQuantity || 1}"
                     min="1"
                     max="10"
                     class="form-control"
                     data-action="input->category-builder#updateSelectionQuantity">
            </div>
            <div class="col-md-4">
              <button type="button"
                      class="btn btn-primary"
                      data-action="click->category-builder#openProductModal"
                      data-category-id="${category.categoryId}"
                      ${!category.categoryName ? 'disabled' : ''}>
                + Add Products
              </button>
              <button type="button"
                      class="btn btn-outline-danger ms-2"
                      data-action="click->category-builder#cancelCategoryCreation"
                      title="Cancel and remove this category">
                Cancel
              </button>
            </div>
          </div>

          ${category.categoryName ? '' : `
            <div class="mt-2">
              <small class="text-muted">Enter a category name to enable product selection</small>
            </div>
          `}
        </div>
      </div>
    `
  }

  // Create HTML for categories table
  createCategoriesTableHtml() {
    console.log('🎨 Creating table HTML for', this.categoriesValue.length, 'categories')

    const rowsHtml = this.categoriesValue.map(category => this.createCategoryRowHtml(category)).join('')

    return `
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>Name</th>
              <th>Products</th>
              <th>Required</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${rowsHtml}
          </tbody>
        </table>
      </div>
    `
  }

  // Create HTML for a single category row
  createCategoryRowHtml(category) {
    const productsCount = category.products.length
    const requiredCount = category.selectionQuantity
    const isComplete = productsCount >= requiredCount
    const statusBadge = isComplete
      ? '<span class="badge bg-success">Complete ✓</span>'
      : '<span class="badge bg-warning">Incomplete</span>'

    const productsText = productsCount === 0
      ? '<span class="text-muted">No products</span>'
      : productsCount === 1
        ? '1 product'
        : `${productsCount} products`

    return `
      <tr data-category-id="${category.categoryId}">
        <td>
          <strong>${category.categoryName || 'Unnamed Category'}</strong>
        </td>
        <td>${productsText}</td>
        <td>${requiredCount}</td>
        <td>${statusBadge}</td>
        <td><small class="text-muted">Just now</small></td>
        <td>
          <div class="btn-group btn-group-sm" role="group">
            <button type="button"
                    class="btn btn-outline-primary"
                    data-action="click->category-builder#viewCategory"
                    data-category-id="${category.categoryId}"
                    title="View products">
              👁️
            </button>
            <button type="button"
                    class="btn btn-outline-secondary"
                    data-action="click->category-builder#editCategory"
                    data-category-id="${category.categoryId}"
                    title="Edit category and manage products">
              ✏️
            </button>
            <button type="button"
                    class="btn btn-outline-danger"
                    data-action="click->category-builder#removeCategory"
                    data-category-id="${category.categoryId}"
                    title="Delete category">
              🗑️
            </button>
          </div>
        </td>
      </tr>
    `
  }

  // Add selected products from modal to category
  addSelectedProducts(event) {
    console.log('🎯 ADD SELECTED PRODUCTS CLICKED!')

    // Get selected products from the modal
    const modal = document.getElementById('productSelectionModal')
    console.log('📋 Modal found:', !!modal)

    const selectedProducts = []

    // Find all checked checkboxes in the modal
    const checkedBoxes = modal.querySelectorAll('input[type="checkbox"]:checked')
    console.log('📋 Found checked boxes:', checkedBoxes.length)

    checkedBoxes.forEach(checkbox => {
      console.log('📦 Processing checkbox:', checkbox.dataset)
      if (checkbox.dataset.productId) {
        // This is a parent product
        const productCard = checkbox.closest('.modern-product-card')
        const productTitle = productCard ? productCard.querySelector('.product-title') : null
        const productName = productTitle ? productTitle.textContent.trim() : 'Unknown Product'

        const product = {
          productId: checkbox.dataset.productId,
          productName: productName,
          variantTitle: productName,  // ← Para compatibilidad con el HTML
          variantSku: checkbox.dataset.productSku || 'N/A',
          productSku: checkbox.dataset.productSku || 'N/A',
          productPrice: checkbox.dataset.productPrice || 0,
          displayOrder: selectedProducts.length  // ← Para el orden
        }

        console.log('✅ Adding product:', product)
        selectedProducts.push(product)
      }
    })

    console.log('📦 Total selected products:', selectedProducts.length)

    if (selectedProducts.length === 0) {
      alert('Please select at least one product to add.')
      return
    }

    // Get the current category ID (stored when modal was opened)
    const categoryId = modal.dataset.currentCategoryId
    if (!categoryId) {
      console.error('No category ID found for adding products')
      return
    }

    // Find the category and add products
    console.log('🔍 Step 1: Looking for category with ID:', categoryId)
    const category = this.findCategory(categoryId)
    console.log('🔍 Step 2: Found category:', !!category, category?.categoryName)

    if (!category) {
      console.error('❌ EARLY EXIT: Category not found for ID:', categoryId)
      return
    }

    console.log('📦 Step 3: Category products before:', category.products.length)

    selectedProducts.forEach((product, index) => {
      console.log(`📦 Step 4.${index}: Processing product:`, product.productName)
      // Check if product already exists in category
      const existingProduct = category.products.find(p => p.productId === product.productId)
      if (!existingProduct) {
        console.log('➕ Adding product to category:', product.productName)
        category.products.push(product)
      } else {
        console.log('⚠️ Product already exists:', product.productName)
      }
    })

    console.log('📦 Step 5: Category products after:', category.products.length)

    // Re-render the entire view (this will switch from inline form to table if needed)
    console.log('🎨 Step 6: Re-rendering categories view after adding products')
    console.log('🎨 Step 6.1: Category now has products:', category.products.length)
    console.log('🎨 Step 6.2: hasIncompleteCategoryWithoutProducts:', this.hasIncompleteCategoryWithoutProducts())
    console.log('🎨 Step 6.3: All categories:', this.categoriesValue.map(c => ({ name: c.categoryName, products: c.products.length })))

    this.renderCategoriesView()

    console.log('💾 Step 7: About to update session data')
    this.updateSessionData()
    console.log('💾 Step 8: Session data updated')

    // Debug: Check current state
    console.log('💾 Step 9: Current categories after update:', this.categoriesValue)
    console.log('💾 Step 10: Session data:', sessionStorage.getItem('bundleCategories'))

    console.log('🚪 Attempting to close modal...')

    // Use Bootstrap method directly (safer)
    const bootstrapModal = bootstrap.Modal.getInstance(modal)
    if (bootstrapModal) {
      console.log('✅ Using Bootstrap hide method...')
      bootstrapModal.hide()
    } else {
      console.log('❌ No Bootstrap instance, creating new one...')
      const newModal = new bootstrap.Modal(modal)
      newModal.hide()
    }

    // Clear all checkboxes
    console.log('🧹 Clearing checkboxes...')
    checkedBoxes.forEach(checkbox => checkbox.checked = false)

    console.log('✅ addSelectedProducts completed!')
  }

  // Update button state based on categories count
  updateButtonState() {
    const categoriesCount = this.categoriesValue.length
    const maxCategories = 12

    // Update button text and state
    if (this.hasAddCategoryButtonTarget) {
      const button = this.addCategoryButtonTarget

      if (categoriesCount >= maxCategories) {
        button.textContent = `Maximum ${maxCategories} Categories Reached`
        button.disabled = true
        button.classList.add('btn-secondary')
        button.classList.remove('btn-primary')
      } else {
        button.textContent = `+ Add Category`
        button.disabled = false
        button.classList.add('btn-primary')
        button.classList.remove('btn-secondary')
      }
    }
  }

  // Clear all rendered categories
  clearAllCategories() {
    const existingCategories = this.categoriesContainerTarget.querySelectorAll('[data-category-id]')
    existingCategories.forEach(category => category.remove())
  }

  // Render a single category
  renderCategory(category) {
    console.log('🎨 Rendering category:', category.categoryName || 'Unnamed')
    const categoryHtml = this.createCategoryHtml(category)
    // Add to categoriesList instead of categoriesContainer
    if (this.hasCategoriesListTarget) {
      console.log('✅ Adding to categoriesListTarget')
      this.categoriesListTarget.insertAdjacentHTML('beforeend', categoryHtml)
    } else {
      console.log('✅ Adding to categoriesContainerTarget')
      this.categoriesContainerTarget.insertAdjacentHTML('beforeend', categoryHtml)
    }

    console.log('🎨 Category rendered successfully')
  }

  // Create HTML for a category (inline version)
  createCategoryHtml(category) {
    console.log('🎨 Creating HTML for category:', category.categoryName, 'Products:', category.products.length)
    console.log('🎨 Products data:', category.products)

    const productsHtml = category.products.map((product, index) => {
      console.log(`🎨 Creating HTML for product ${index}:`, product)
      return this.createProductInCategoryHtml(product, index)
    }).join('')

    console.log('🎨 Generated products HTML:', productsHtml)

    return `
      <div class="card mb-3" data-category-id="${category.categoryId}">
        <!-- Category Header - Inline Form -->
        <div class="card-header bg-light">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="text-muted" style="cursor: move;">≡</span>
            </div>
            <div class="col">
              <input type="text"
                     name="categoryName"
                     value="${category.categoryName}"
                     placeholder="Category Name (e.g., Protein Powders)"
                     class="form-control form-control-sm border-0 bg-transparent"
                     data-action="input->category-builder#updateCategoryName">
            </div>
            <div class="col-auto">
              <div class="input-group input-group-sm">
                <span class="input-group-text">Qty:</span>
                <input type="number"
                       name="selectionQuantity"
                       value="${category.selectionQuantity}"
                       min="1"
                       max="10"
                       class="form-control"
                       style="max-width: 70px;"
                       data-action="input->category-builder#updateSelectionQuantity">
              </div>
            </div>
            <div class="col-auto">
              <button type="button"
                      class="btn btn-primary btn-sm me-2"
                      data-action="click->category-builder#openProductModal"
                      data-category-id="${category.categoryId}"
                      title="Add products to category">
                + Add Products
              </button>
              <button type="button"
                      class="btn btn-outline-danger btn-sm"
                      data-action="click->category-builder#removeCategory"
                      title="Remove category">
                ×
              </button>
            </div>
          </div>
        </div>

        <!-- Products List -->
        <div class="card-body">
          <div class="products-container">

            <!-- Products List -->
            <div data-category-products>
              ${productsHtml}
            </div>

            <!-- Empty State -->
            <div class="text-center text-muted p-4 ${category.products.length > 0 ? 'd-none' : ''}" data-empty-state>
              <div class="mb-2" style="font-size: 2rem;">📦</div>
              <p class="mb-1">No products added yet</p>
              <small>Click "Add Products" to select products for this category</small>
            </div>

            <!-- Products Counter -->
            <div class="mt-2 small text-muted ${category.products.length === 0 ? 'd-none' : ''}" data-products-counter>
              <strong>${category.products.length}</strong> products added
              ${category.products.length > category.selectionQuantity ?
                `<span class="text-warning">(${category.products.length - category.selectionQuantity} more than needed)</span>` :
                category.products.length === category.selectionQuantity ?
                  '<span class="text-success">✓</span>' :
                  `<span class="text-primary">(need ${category.selectionQuantity - category.products.length} more)</span>`
              }
            </div>
          </div>
        </div>
      </div>
    `
  }

  // Create HTML for a product within a category
  createProductInCategoryHtml(product, displayOrder) {
    const isDefault = displayOrder === 0
    return `
      <div class="product-in-category d-flex align-items-center justify-content-between p-2 mb-2 bg-white border rounded ${isDefault ? 'border-primary border-2' : 'border-secondary'}"
           data-product-id="${product.productId}"
           data-display-order="${displayOrder}">
        <div class="d-flex align-items-center">
          <div class="drag-handle me-3 text-muted" style="cursor: move;">
            <i class="fas fa-grip-vertical"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-1 fw-medium">${product.variantTitle}</h6>
            <small class="text-muted">SKU: ${product.variantSku}</small>
            ${isDefault ? '<div><small class="text-primary fw-bold">Default selection</small></div>' : ''}
          </div>
        </div>
        <button type="button"
                class="btn btn-outline-danger btn-sm"
                data-action="click->category-builder#removeProduct"
                title="Remove product">
          ×
        </button>
      </div>
    `
  }



  // View category products (modal or expanded view)
  viewCategory(event) {
    const categoryId = event.target.dataset.categoryId
    const category = this.findCategory(categoryId)

    if (!category) {
      console.error('Category not found:', categoryId)
      return
    }

    console.log('👁️ Viewing category:', category.categoryName)

    const categoryName = category.categoryName || 'Unnamed Category'
    const productsCount = category.products.length

    if (productsCount === 0) {
      alert(`"${categoryName}" has no products yet.\n\nClick "Add Products" to add products to this category.`)
      return
    }

    // Create a simple products list for the alert
    const productsList = category.products.map((product, index) =>
      `${index + 1}. ${product.variantTitle || product.productName} (SKU: ${product.variantSku || product.productSku})`
    ).join('\n')

    alert(`"${categoryName}" Products (${productsCount}):\n\n${productsList}`)
  }

  // Edit category (modal)
  editCategory(event) {
    const categoryId = event.target.dataset.categoryId
    const category = this.findCategory(categoryId)

    if (!category) {
      console.error('Category not found:', categoryId)
      return
    }

    console.log('✏️ Editing category:', category.categoryName)

    // For now, we'll use prompts. Later we can create a proper modal
    const categoryName = category.categoryName || ''
    const productsCount = category.products.length

    // Edit category name
    const newName = prompt('Category name:', categoryName)
    if (newName === null) return // User cancelled

    if (newName.trim() === '') {
      alert('Category name cannot be empty.')
      return
    }

    // Edit selection quantity
    const currentQty = category.selectionQuantity || 1
    const newQty = prompt(`Required selection quantity (currently ${currentQty}):`, currentQty)
    if (newQty === null) return // User cancelled

    const qty = parseInt(newQty)
    if (isNaN(qty) || qty < 1) {
      alert('Selection quantity must be a positive number.')
      return
    }

    // Ask about managing products
    const manageProducts = confirm(`Category updated!\n\nDo you want to manage products for "${newName.trim()}"?\n\nCurrent products: ${productsCount}`)

    // Update category
    category.categoryName = newName.trim()
    category.selectionQuantity = qty

    // Re-render the view
    this.renderCategoriesView()
    this.updateSessionData()

    // Open product modal if requested
    if (manageProducts) {
      // Simulate clicking the product modal button
      setTimeout(() => {
        this.openProductModal({ target: { dataset: { categoryId } } })
      }, 100)
    }
  }

  // Update category name
  updateCategoryName(event) {
    const newName = event.target.value.trim()

    // For inline form, find the category without products
    const category = this.getFirstIncompleteCategoryWithoutProducts()
    if (category) {
      category.categoryName = newName

      // Enable/disable Add Products button based on name
      const addProductsBtn = event.target.closest('.card').querySelector('[data-action*="openProductModal"]')
      if (addProductsBtn) {
        addProductsBtn.disabled = !newName
      }

      this.updateSessionData()
      return
    }

    // For table/card view, find by category ID
    const categoryCard = event.target.closest('[data-category-id]')
    if (!categoryCard) {
      console.error('Could not find category card')
      return
    }

    const categoryId = categoryCard.dataset.categoryId
    const foundCategory = this.findCategory(categoryId)
    if (foundCategory) {
      foundCategory.categoryName = newName
      this.updateSessionData()
    }
  }

  // Cancel category creation (remove incomplete category)
  cancelCategoryCreation(event) {
    const incompleteCategory = this.getFirstIncompleteCategoryWithoutProducts()
    if (incompleteCategory) {
      // Remove the incomplete category
      this.categoriesValue = this.categoriesValue.filter(cat => cat.categoryId !== incompleteCategory.categoryId)

      // Re-render view (will show empty state or table)
      this.renderCategoriesView()
      this.updateButtonState()
      this.updateSessionData()

      console.log('✅ Cancelled category creation')
    }
  }

  // Update selection quantity
  updateSelectionQuantity(event) {
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId
    const newQuantity = parseInt(event.target.value) || 1

    const category = this.findCategory(categoryId)
    if (category) {
      category.selectionQuantity = newQuantity
      this.updateProductsCounter(categoryCard, category)
      this.updateEmptyStateText(categoryCard, newQuantity)
      this.updateSessionData()
    }
  }

  // Open product selection modal for a category
  openProductModal(event) {
    const categoryId = event.target.dataset.categoryId
    console.log(`📂 Opening product modal for category: ${categoryId}`)
    console.log(`📊 Available categories:`, this.categoriesValue.map(c => ({ id: c.categoryId, name: c.categoryName })))

    // Find the category data
    const category = this.findCategory(categoryId)
    if (!category) {
      console.error('Category not found:', categoryId)
      console.error('Available category IDs:', this.categoriesValue.map(c => c.categoryId))
      return
    }

    // Store category data in the modal element
    const modalElement = document.getElementById('productSelectionModal')
    modalElement.dataset.currentCategoryId = categoryId
    modalElement.dataset.categoryName = category.categoryName || 'Unnamed Category'
    modalElement.dataset.selectionLimit = category.selectionQuantity || 1
    modalElement.dataset.currentlySelected = category.products ? category.products.length : 0

    // Update modal title with category info
    const modalTitle = modalElement.querySelector('#productSelectionModalLabel')
    if (modalTitle) {
      const currentCount = category.products ? category.products.length : 0
      const limit = category.selectionQuantity || 1
      modalTitle.textContent = `Add Products to Category: ${category.categoryName || 'Unnamed'} (${currentCount}/${limit} selected)`
    }

    console.log(`📊 Category: ${category.categoryName}, Limit: ${category.selectionQuantity}, Current: ${category.products ? category.products.length : 0}`)

    // Show the modal (assuming Bootstrap 5)
    const modal = new bootstrap.Modal(modalElement)
    modal.show()
  }

  // Remove category
  removeCategory(event) {
    const categoryId = event.target.dataset.categoryId
    const category = this.findCategory(categoryId)

    if (!category) {
      console.error('Category not found:', categoryId)
      return
    }

    const categoryName = category.categoryName || 'Unnamed Category'
    const productsCount = category.products.length

    let confirmMessage = `Are you sure you want to remove "${categoryName}"?`
    if (productsCount > 0) {
      confirmMessage += `\n\nThis will also remove ${productsCount} product(s) from this category.`
    }

    if (confirm(confirmMessage)) {
      // Remove from data
      this.categoriesValue = this.categoriesValue.filter(cat => cat.categoryId !== categoryId)

      // Re-render the view (table or empty state)
      this.renderCategoriesView()

      // Update button state
      this.updateButtonState()

      this.updateSessionData()

      console.log(`✅ Removed category: ${categoryName}`)
    }
  }

  // Remove product from category
  removeProduct(event) {
    const productElement = event.target.closest('.product-in-category')
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId
    const productId = productElement.dataset.productId

    const category = this.findCategory(categoryId)
    if (category) {
      // Remove product from data
      category.products = category.products.filter(p => p.productId !== productId)
      
      // Remove from DOM
      productElement.remove()
      
      // Update UI
      this.updateCategoryUI(categoryCard, category)
      this.updateSessionData()
    }
  }

  // Drag & Drop handlers
  dragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  dragEnter(event) {
    event.preventDefault()
    const dropZone = event.target.closest('.products-drop-zone')
    if (dropZone) {
      dropZone.classList.add('border-blue-500', 'bg-blue-50')
    }
  }

  dragLeave(event) {
    const dropZone = event.target.closest('.products-drop-zone')
    if (dropZone && !dropZone.contains(event.relatedTarget)) {
      dropZone.classList.remove('border-blue-500', 'bg-blue-50')
    }
  }

  drop(event) {
    event.preventDefault()
    const dropZone = event.target.closest('.products-drop-zone')
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId

    // Remove drag styling
    dropZone.classList.remove('border-blue-500', 'bg-blue-50')

    try {
      const productData = JSON.parse(event.dataTransfer.getData('application/json'))
      this.addProductToCategory(categoryId, productData)
    } catch (error) {
      console.error('Error parsing dropped product data:', error)
    }
  }

  // Add product to category
  addProductToCategory(categoryId, productData) {
    const category = this.findCategory(categoryId)
    if (!category) return

    // Check if product already exists in category
    const existingProduct = category.products.find(p => p.productId === productData.id)
    if (existingProduct) {
      alert('This product is already in this category')
      return
    }

    // Create product object
    const newProduct = {
      productId: productData.id,
      variantTitle: productData.name,
      variantSku: productData.sku,
      displayOrder: category.products.length,
      isDefault: category.products.length === 0 // First product is default
    }

    // Add to data
    category.products.push(newProduct)

    // Update UI
    const categoryCard = this.element.querySelector(`[data-category-id="${categoryId}"]`)
    this.updateCategoryUI(categoryCard, category)
    this.updateSessionData()
  }

  // Update category UI after changes
  updateCategoryUI(categoryCard, category) {
    const productsList = categoryCard.querySelector('[data-category-products]')
    const emptyState = categoryCard.querySelector('[data-empty-state]')
    
    // Update products list
    const productsHtml = category.products.map((product, index) => 
      this.createProductInCategoryHtml(product, index)
    ).join('')
    productsList.innerHTML = productsHtml

    // Update empty state visibility
    if (category.products.length > 0) {
      emptyState.classList.add('hidden')
    } else {
      emptyState.classList.remove('hidden')
    }

    // Update products counter
    this.updateProductsCounter(categoryCard, category)
  }

  // Update products counter
  updateProductsCounter(categoryCard, category) {
    const counter = categoryCard.querySelector('[data-products-counter]')
    const count = category.products.length
    const needed = category.selectionQuantity

    if (count === 0) {
      counter.classList.add('hidden')
    } else {
      counter.classList.remove('hidden')
      
      let statusText = ''
      if (count > needed) {
        statusText = `<span class="text-orange-600">(${count - needed} more than needed)</span>`
      } else if (count === needed) {
        statusText = '<span class="text-green-600">✓</span>'
      } else {
        statusText = `<span class="text-blue-600">(need ${needed - count} more)</span>`
      }

      counter.innerHTML = `<span class="font-medium">${count}</span> products added ${statusText}`
    }
  }

  // Update empty state text
  updateEmptyStateText(categoryCard, quantity) {
    const emptyState = categoryCard.querySelector('[data-empty-state] p:last-child')
    if (emptyState) {
      emptyState.textContent = `Customer can select ${quantity} product(s) from this category`
    }
  }

  // Continue to preview step
  continueToPreview() {
    if (this.categoriesValue.length === 0) {
      alert('Please create at least one category before continuing.')
      return
    }

    // Check if all categories have products
    const categoriesWithoutProducts = this.categoriesValue.filter(cat => cat.products.length === 0)
    if (categoriesWithoutProducts.length > 0) {
      const categoryNames = categoriesWithoutProducts.map(cat => cat.categoryName || 'Unnamed Category').join(', ')
      if (!confirm(`The following categories have no products: ${categoryNames}. Continue anyway?`)) {
        return
      }
    }

    // Update session data and submit form
    this.updateSessionData()

    // Submit the form to continue to preview
    const form = this.element.closest('form')
    if (form) {
      // Update hidden input with categories data
      const categoriesInput = document.getElementById('categories_data_input')
      if (categoriesInput) {
        categoriesInput.value = JSON.stringify(this.categoriesValue)
      }

      form.submit()
    }
  }

  // Update session data via AJAX
  updateSession() {
    // This will be called when categories are updated
    // For now, we'll just update the hidden input
    const categoriesInput = document.getElementById('categories_data_input')
    if (categoriesInput) {
      categoriesInput.value = JSON.stringify(this.categoriesValue)
    }

    // Update continue button state
    this.updateContinueButton()
  }

  // Update continue button state
  updateContinueButton() {
    const continueBtn = document.getElementById('continueToPreview')

    if (continueBtn) {
      continueBtn.disabled = this.categoriesValue.length === 0
    }
  }

  // Re-render a specific category with provided data
  rerenderCategoryWithData(categoryId, categoryData) {
    console.log('🎨 Rerendering category with provided data:', categoryId)
    console.log('🎨 Provided category data:', categoryData)
    console.log('🎨 Products in provided data:', categoryData.products.length)

    const existingCategoryCard = this.element.querySelector(`[data-category-id="${categoryId}"]`)
    console.log('🔍 Found existing card:', !!existingCategoryCard)

    if (!existingCategoryCard) {
      console.error('❌ Category card not found for ID:', categoryId)
      return
    }

    const newCategoryHtml = this.createCategoryHtml(categoryData)
    console.log('🎨 Generated new HTML length:', newCategoryHtml.length)

    existingCategoryCard.outerHTML = newCategoryHtml
    console.log('✅ Category re-rendered successfully with provided data')
  }

  // Re-render a specific category
  rerenderCategory(categoryId) {
    console.log('🎨 Rerendering category:', categoryId)
    console.log('🎨 Looking for selector:', `[data-category-id="${categoryId}"]`)

    const existingCategoryCard = this.element.querySelector(`[data-category-id="${categoryId}"]`)
    console.log('🔍 Found existing card:', !!existingCategoryCard)

    if (!existingCategoryCard) {
      console.error('❌ Category card not found for ID:', categoryId)
      console.log('🔍 Available category cards:')
      const allCards = this.element.querySelectorAll('[data-category-id]')
      allCards.forEach(card => {
        console.log('  - Found card with ID:', card.dataset.categoryId)
      })
      return
    }

    const category = this.findCategory(categoryId)
    console.log('🔍 Found category data:', !!category, 'Products:', category?.products?.length)

    if (category) {
      const newCategoryHtml = this.createCategoryHtml(category)
      console.log('🎨 Generated new HTML length:', newCategoryHtml.length)
      console.log('🎨 New HTML preview:', newCategoryHtml.substring(0, 200) + '...')

      existingCategoryCard.outerHTML = newCategoryHtml
      console.log('✅ Category re-rendered successfully')
    } else {
      console.error('❌ Category data not found for ID:', categoryId)
    }
  }

  // Helper methods
  findCategory(categoryId) {
    return this.categoriesValue.find(cat => cat.categoryId === categoryId)
  }

  // Check if there's a category without products (incomplete)
  hasIncompleteCategoryWithoutProducts() {
    return this.categoriesValue.some(cat => cat.products.length === 0)
  }

  // Get the first category without products
  getFirstIncompleteCategoryWithoutProducts() {
    return this.categoriesValue.find(cat => cat.products.length === 0)
  }

  updateSessionData() {
    // Update the Stimulus value
    this.categoriesValue = [...this.categoriesValue]

    // Update continue button
    this.updateContinueButton()

    // Dispatch event to update session
    this.dispatch('categoriesUpdated', {
      detail: { categories: this.categoriesValue }
    })
  }

  // Global drag & drop setup (for cleanup)
  setupGlobalDragListeners() {
    // Add any global listeners if needed
  }

  cleanupGlobalDragListeners() {
    // Clean up any global listeners
  }
}
