import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "categoryGrid",
    "productsGrid",
    "productsTitle",
    "dropZone",
    "emptyState",
    "selectedProducts",
    "productCount",
    "productCountBadge",
    "searchInput",
    "saveButton",
    "form",
    "bundleDataInput",
    "bundleSummary"
  ]

  static values = {
    availableProducts: Array,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected!")
    console.log("🏗️ Bundle products:", this.bundleProductsValue)

    // Initialize data
    this.bundleProductsValue = this.bundleProductsValue || []
    this.currentCategory = 'supplements'

    // Setup drag and drop
    this.setupDragAndDrop()

    // Setup checkbox interactions
    this.setupCheckboxInteractions()

    // Load initial products from first category
    this.loadInitialProducts()

    // Update UI
    this.updateUI()
  }

  // Load products from the first active category
  loadInitialProducts() {
    const firstActiveCategory = this.categoryGridTarget.querySelector('.category-card.active')
    if (firstActiveCategory) {
      this.loadProductsFromCategory(firstActiveCategory)
    }
  }

  // Handle category selection
  selectCategory(event) {
    const categoryCard = event.currentTarget

    // Update active category
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active')
    })
    categoryCard.classList.add('active')

    // Load products for this category
    this.loadProductsFromCategory(categoryCard)
  }

  // Load products from category data
  loadProductsFromCategory(categoryCard) {
    const categoryId = categoryCard.dataset.categoryId
    const categoryName = categoryCard.dataset.categoryName
    const productsData = categoryCard.dataset.products

    console.log(`Loading products for category: ${categoryName}`)

    try {
      const products = JSON.parse(productsData)

      // Update products title with category name and count
      if (this.hasProductsTitleTarget) {
        this.productsTitleTarget.textContent = `${categoryName} (${products.length})`
      }

      this.renderProducts(products)
    } catch (error) {
      console.error('Error parsing products data:', error)
      this.showProductsError()
    }
  }

  // Render products in the grid
  renderProducts(products) {
    if (!products || products.length === 0) {
      this.showNoProducts()
      return
    }

    const productsHTML = products.map(product => `
      <div class="modern-product-card"
           draggable="true"
           data-product-id="${product.id}"
           data-product-data='${JSON.stringify(product).replace(/'/g, '&apos;')}'
           data-action="dragstart->bundle-builder#handleDragStart dragend->bundle-builder#handleDragEnd click->bundle-builder#addProduct">

        <!-- Product Header -->
        <div class="product-card-header">
          <div class="product-image-container">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.name || product.title}" loading="lazy">` :
              `<div class="product-image-placeholder">${product.emoji || '📦'}</div>`
            }
          </div>

          <div class="product-info-section">
            <h4 class="product-title">${product.name || product.title}</h4>

            <div class="product-meta">
              ${product.sku ? `<span class="product-sku">SKU: ${product.sku}</span>` : ''}
              <span class="product-price">${product.price || '$0.00'}</span>
              <div class="product-status">
                <span class="status-badge in-stock">✅ Available</span>
              </div>
            </div>

            ${product.description ?
              `<div class="product-description">${product.description.substring(0, 80)}${product.description.length > 80 ? '...' : ''}</div>` :
              ''
            }
          </div>
        </div>

        <!-- Selection Controls -->
        <div class="product-selection-controls">
          <div class="product-checkbox-container">
            <input type="checkbox"
                   class="product-checkbox-input"
                   data-product-id="${product.id}"
                   id="builder-product-${product.id}">
            <div class="product-checkbox"></div>
            <label for="builder-product-${product.id}" class="checkbox-label">
              Add to Bundle
            </label>
          </div>

          <div class="product-actions">
            <button type="button" class="action-btn primary" title="Add to bundle">
              ➕ Add
            </button>
          </div>
        </div>
      </div>
    `).join('')

    this.productsGridTarget.innerHTML = productsHTML
  }

  // Show no products message
  showNoProducts() {
    this.productsGridTarget.innerHTML = `
      <div class="text-center py-4 text-muted">
        <div class="mb-2">📦</div>
        <div>No products found in this category</div>
      </div>
    `
  }

  // Show products error
  showProductsError() {
    this.productsGridTarget.innerHTML = `
      <div class="col-12">
        <div class="text-center py-4 text-danger">
          <div class="mb-2">⚠️</div>
          <div>Error loading products</div>
        </div>
      </div>
    `
  }

  // Debug method to add a test product
  debugAddProduct() {
    const testProduct = {
      id: Date.now(),
      title: "Test Product",
      price: "19.99",
      image_url: null
    }

    console.log("🧪 Adding test product:", testProduct)
    this.addProductToBundle(testProduct)
  }

  // Setup drag and drop functionality
  setupDragAndDrop() {
    if (this.hasDropZoneTarget) {
      this.dropZoneTarget.addEventListener('dragover', this.handleDragOver.bind(this))
      this.dropZoneTarget.addEventListener('dragleave', this.handleDragLeave.bind(this))
      this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this))
    }
  }

  // Setup checkbox interactions
  setupCheckboxInteractions() {
    // Listen for checkbox changes
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('product-checkbox-input')) {
        this.handleProductCheckbox(e.target)
      }
    })

    // Listen for card clicks to toggle checkboxes
    document.addEventListener('click', (e) => {
      const card = e.target.closest('.modern-product-card')
      if (card && !e.target.closest('.product-actions') &&
          !e.target.closest('.product-checkbox-container')) {
        const checkbox = card.querySelector('.product-checkbox-input')
        if (checkbox) {
          checkbox.checked = !checkbox.checked
          this.handleProductCheckbox(checkbox)
        }
      }
    })
  }

  // Handle product checkbox changes
  handleProductCheckbox(checkbox) {
    const productId = checkbox.dataset.productId
    const card = checkbox.closest('.modern-product-card')
    const visualCheckbox = card.querySelector('.product-checkbox')

    if (checkbox.checked) {
      // Add visual feedback
      card.classList.add('selected')
      visualCheckbox.classList.add('checked')

      // Get product data and add to bundle
      const productDataString = card.dataset.productData
      if (productDataString) {
        try {
          const productData = JSON.parse(productDataString)
          this.addProductToBundle(productData)
        } catch (error) {
          console.error('Error parsing product data:', error)
        }
      }
    } else {
      // Remove visual feedback
      card.classList.remove('selected')
      visualCheckbox.classList.remove('checked')

      // Remove from bundle
      this.bundleProductsValue = this.bundleProductsValue.filter(
        p => p.id.toString() !== productId
      )
      this.updateUI()
    }
  }

  // Handle drag start
  handleDragStart(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      event.dataTransfer.setData('text/plain', productData)
      event.dataTransfer.effectAllowed = 'copy'
      event.currentTarget.classList.add('dragging')
      console.log("🎯 Drag started for product:", JSON.parse(productData).title)
    }
  }

  // Handle drag end
  handleDragEnd(event) {
    event.currentTarget.classList.remove('dragging')
  }

  // Handle drag over
  handleDragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
    this.dropZoneTarget.classList.add('drag-over')
  }

  // Handle drag leave
  handleDragLeave(event) {
    // Only remove drag-over if we're actually leaving the drop zone
    if (!this.dropZoneTarget.contains(event.relatedTarget)) {
      this.dropZoneTarget.classList.remove('drag-over')
    }
  }

  // Handle drop
  handleDrop(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.remove('drag-over')

    try {
      const productDataString = event.dataTransfer.getData('text/plain')
      console.log("📦 Raw product data:", productDataString)

      if (productDataString) {
        const productData = JSON.parse(productDataString)
        console.log("📦 Product dropped:", productData)
        this.addProductToBundle(productData)
      } else {
        console.warn("No product data found in drop event")
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  // Add product to bundle (via click or drag)
  addProduct(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      try {
        const product = JSON.parse(productData)
        console.log("👆 Product clicked:", product)
        this.addProductToBundle(product)
      } catch (error) {
        console.error('Error parsing product data:', error)
      }
    }
  }

  // Add product to bundle
  addProductToBundle(product) {
    console.log('🔍 Checking if product exists in bundle:', product)
    console.log('🔍 Current bundle products:', this.bundleProductsValue)

    // Check if product already exists
    const existingProduct = this.bundleProductsValue.find(p => p.id.toString() === product.id.toString())
    if (existingProduct) {
      console.log('⚠️ Product already in bundle:', existingProduct)
      return
    }

    // Add to bundle
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log(`✅ Added ${product.name || product.title} to bundle. New count: ${this.bundleProductsValue.length}`)

    // Update UI
    this.updateUI()
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    this.bundleProductsValue = this.bundleProductsValue.filter(
      p => p.id.toString() !== productId
    )

    console.log(`Removed product ${productId} from bundle`)
    this.updateUI()
  }

  // Clear all products from bundle
  clearBundle() {
    if (this.bundleProductsValue.length === 0) return

    if (confirm('Are you sure you want to clear all products from the bundle?')) {
      this.bundleProductsValue = []
      this.updateUI()
    }
  }

  // Update UI based on current state
  updateUI() {
    this.updateProductCount()
    this.updateSelectedProducts()
    this.updateSaveButton()
    this.updateFormData()
    this.updateProgressSemaphore()
    this.notifyWizardController()
  }

  // Update product count displays
  updateProductCount() {
    const count = this.bundleProductsValue.length

    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = count.toString()
    }

    if (this.hasProductCountBadgeTarget) {
      this.productCountBadgeTarget.textContent = count.toString()
    }

    // Update navigation button state
    this.updateNavigationButton()
  }

  // Update navigation button state
  updateNavigationButton() {
    console.log("🔘 updateNavigationButton called")

    // Find the next button by its data-wizard-target attribute
    const nextButton = document.querySelector('[data-wizard-target="nextButton"]')
    console.log("🔘 Found nextButton:", nextButton)

    if (nextButton) {
      const hasProducts = this.bundleProductsValue.length > 0
      nextButton.disabled = !hasProducts

      console.log(`🔘 Navigation button updated: hasProducts=${hasProducts}, disabled=${!hasProducts}`)
      console.log(`🔘 Button element:`, nextButton)
    } else {
      console.log("🔘 No nextButton found!")
    }
  }

  // Update progress semaphore
  updateProgressSemaphore() {
    const hasProducts = this.bundleProductsValue.length > 0

    // Update step 2 (current step) based on whether products are selected
    const step2 = document.querySelector('.progress-step:nth-child(3)')
    if (step2) {
      if (hasProducts) {
        step2.classList.add('active', 'completed')
      } else {
        step2.classList.add('active')
        step2.classList.remove('completed')
      }
    }
  }

  // Notify wizard controller about state changes
  notifyWizardController() {
    const hasProducts = this.bundleProductsValue.length > 0

    // Dispatch custom event that wizard controller can listen to
    const event = new CustomEvent('bundle-builder:state-changed', {
      detail: {
        hasProducts: hasProducts,
        productCount: this.bundleProductsValue.length,
        products: this.bundleProductsValue,
        canProceed: hasProducts
      },
      bubbles: true
    })

    this.element.dispatchEvent(event)
    console.log(`🔔 Notified wizard controller: hasProducts=${hasProducts}, count=${this.bundleProductsValue.length}`)
  }

  // Update selected products display
  updateSelectedProducts() {
    const hasProducts = this.bundleProductsValue.length > 0

    console.log(`🔄 Updating UI - Has products: ${hasProducts}, Count: ${this.bundleProductsValue.length}`)

    // Show/hide empty state
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = hasProducts ? 'none' : 'flex'
      console.log(`👁️ Empty state display: ${this.emptyStateTarget.style.display}`)
    }

    // Show/hide selected products
    if (this.hasSelectedProductsTarget) {
      this.selectedProductsTarget.style.display = hasProducts ? 'block' : 'none'
      console.log(`👁️ Selected products display: ${this.selectedProductsTarget.style.display}`)

      if (hasProducts) {
        const productsHTML = this.renderSelectedProducts()
        // Put the HTML inside the row container, not replace the entire container
        const rowContainer = this.selectedProductsTarget.querySelector('.row')
        if (rowContainer) {
          rowContainer.innerHTML = productsHTML
        } else {
          this.selectedProductsTarget.innerHTML = `<div class="row g-1">${productsHTML}</div>`
        }
        console.log(`📦 Rendered ${this.bundleProductsValue.length} products`)
      } else {
        const rowContainer = this.selectedProductsTarget.querySelector('.row')
        if (rowContainer) {
          rowContainer.innerHTML = ''
        } else {
          this.selectedProductsTarget.innerHTML = '<div class="row g-1"></div>'
        }
      }
    }
  }

  // Render selected products
  renderSelectedProducts() {
    return this.bundleProductsValue.map(product => `
      <div class="col-12">
        <div class="bundle-product-card">
          <div class="bundle-product-image">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.name || product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">` :
              `<span style="font-size: 1.5rem;">${product.emoji || '📦'}</span>`
            }
          </div>
          <div class="bundle-product-info">
            <div class="bundle-product-name">${product.name || product.title}</div>
            <div class="bundle-product-price">${product.price || '$0.00'}</div>
          </div>
          <button type="button"
                  class="bundle-product-remove"
                  data-product-id="${product.id}"
                  data-action="click->bundle-builder#removeProduct"
                  title="Remove product">
            ×
          </button>
        </div>
      </div>
    `).join('')
  }

  // Update save button state
  updateSaveButton() {
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = this.bundleProductsValue.length === 0
    }
  }

  // Update form data for submission
  updateFormData() {
    if (this.hasBundleDataInputTarget) {
      const bundleData = {
        categories: [], // For now, we're not using categories in the new flow
        products: this.bundleProductsValue.map(product => ({
          id: product.id,
          name: product.name || product.title,
          title: product.title || product.name,
          price: product.price,
          sku: product.sku,
          image_url: product.image_url,
          emoji: product.emoji
        }))
      }

      this.bundleDataInputTarget.value = JSON.stringify(bundleData)
      console.log(`📝 Updated form data:`, bundleData)
    }
  }
}
