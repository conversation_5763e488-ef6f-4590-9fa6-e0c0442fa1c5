/* Enhanced Product Search - Modern UI Components */

/* Search Container */
.enhanced-search-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24px;
}

/* Search Header */
.search-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.search-title {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stats-main {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.stats-filter {
  font-size: 13px;
  color: #007bff;
  font-style: italic;
}

.stats-note {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.product-count-badge {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
}

.product-count-badge.large {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}

.product-count-badge.huge {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

/* Search Form */
.search-form {
  padding: 20px;
  background: white;
}

.search-form-inner {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Main Search Input */
.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  background: #f8f9ff;
}

.search-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

.keyboard-hint {
  position: absolute;
  right: 80px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 11px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  pointer-events: none;
  opacity: 0.7;
}

.search-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.search-btn:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.search-btn:active {
  transform: translateY(0);
}

/* Advanced Filters */
.search-filters {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 8px 8px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 140px;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-select:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.clear-filters-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.clear-filters-btn:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
}

/* Quick Filters */
.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.quick-filter-btn {
  padding: 6px 12px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.quick-filter-btn:hover {
  border-color: #007bff;
  color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.quick-filter-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* Search Results */
.search-results-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.results-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-title {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.results-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.view-toggle {
  display: flex;
  background: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.view-toggle-btn {
  padding: 6px 12px;
  background: transparent;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.view-toggle-btn.active {
  background: #007bff;
  color: white;
}

.bulk-actions {
  display: flex;
  gap: 8px;
}

.bulk-action-btn {
  padding: 6px 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bulk-action-btn:hover {
  background: #1e7e34;
  transform: translateY(-1px);
}

.bulk-action-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Empty States */
.empty-search {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-description {
  font-size: 14px;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Loading States */
.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-header {
    padding: 16px;
  }
  
  .search-form {
    padding: 16px;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-group {
    min-width: auto;
    width: 100%;
  }
  
  .keyboard-hint {
    display: none;
  }
  
  .search-input-group {
    flex-direction: column;
  }
  
  .search-btn {
    width: 100%;
  }
  
  .results-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .results-actions {
    justify-content: space-between;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .search-input {
    border-width: 3px;
  }
  
  .filter-select {
    border-width: 3px;
  }
  
  .quick-filter-btn {
    border-width: 3px;
  }
}
