/* Modern Product Cards - Enhanced Visual Design */

/* Product Card Container */
.modern-product-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.modern-product-card:hover {
  border-color: #007bff;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.modern-product-card.selected {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
}

/* Product Header */
.product-card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

/* Product Image */
.product-image-container {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  border: 2px solid #e9ecef;
}

.product-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image-container:hover img {
  transform: scale(1.05);
}

.product-image-placeholder {
  font-size: 32px;
  color: #6c757d;
  opacity: 0.7;
}

/* Product Info */
.product-info-section {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  margin-bottom: 8px;
}

.product-sku {
  background: #f8f9fa;
  color: #495057;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.product-price {
  color: #28a745;
  font-weight: 700;
  font-size: 16px;
}

.product-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.in-stock {
  background: #d4edda;
  color: #155724;
}

.status-badge.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

/* Product Description */
.product-description {
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Selection Controls */
.product-selection-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.product-checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.product-checkbox:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.product-checkbox.checked {
  background: #007bff;
  border-color: #007bff;
}

.product-checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  user-select: none;
}

/* Action Buttons */
.product-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  border-color: #007bff;
  color: #007bff;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.action-btn.primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.action-btn.primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  color: white;
}

.action-btn.success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.action-btn.success:hover {
  background: #1e7e34;
  border-color: #1e7e34;
  color: white;
}

/* Variants Section */
.product-variants {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.variants-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.variants-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.variants-count {
  background: #e9ecef;
  color: #495057;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.variants-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.variant-item:hover {
  background: #e3f2fd;
  border-color: #007bff;
}

.variant-item.selected {
  background: #d4edda;
  border-color: #28a745;
}

.variant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.variant-title {
  font-size: 13px;
  font-weight: 500;
  color: #212529;
}

.variant-details {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.variant-price {
  color: #28a745;
  font-weight: 600;
}

.variant-stock {
  display: flex;
  align-items: center;
  gap: 4px;
}

.variant-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #ced4da;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.variant-checkbox:hover {
  border-color: #007bff;
}

.variant-checkbox.checked {
  background: #007bff;
  border-color: #007bff;
}

.variant-checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-card-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .product-image-container {
    width: 100%;
    height: 120px;
    align-self: center;
  }
  
  .product-selection-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .product-actions {
    justify-content: center;
  }
  
  .variants-list {
    gap: 6px;
  }
  
  .variant-item {
    padding: 6px 8px;
  }
}

/* Animation for selection */
@keyframes selectPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.modern-product-card.selecting {
  animation: selectPulse 0.3s ease;
}

/* Loading state */
.modern-product-card.loading {
  opacity: 0.6;
  pointer-events: none;
}

.modern-product-card.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
