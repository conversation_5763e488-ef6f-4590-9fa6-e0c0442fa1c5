// Enhanced Product Search - Modern JavaScript Implementation
// Handles advanced search, filtering, and product selection with checkboxes

class EnhancedProductSearch {
  constructor() {
    this.selectedProducts = new Set();
    this.selectedVariants = new Set();
    this.searchTimeout = null;
    this.currentView = 'grid'; // 'grid' or 'list'
    
    this.init();
  }

  init() {
    console.log('🔍 Enhanced Product Search initialized');
    
    // Bind event listeners
    this.bindSearchEvents();
    this.bindFilterEvents();
    this.bindSelectionEvents();
    this.bindKeyboardShortcuts();
    
    // Initialize UI state
    this.updateBulkActions();
    this.setupViewToggle();
  }

  // Search Events
  bindSearchEvents() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
          this.performSearch(e.target.value);
        }, 300);
      });
    }

    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const query = document.querySelector('.search-input')?.value || '';
        this.performSearch(query);
      });
    }
  }

  // Filter Events
  bindFilterEvents() {
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
      select.addEventListener('change', () => {
        this.applyFilters();
      });
    });

    const clearFiltersBtn = document.querySelector('.clear-filters-btn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearAllFilters();
      });
    }

    // Quick filters
    const quickFilterBtns = document.querySelectorAll('.quick-filter-btn');
    quickFilterBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleQuickFilter(btn);
      });
    });
  }

  // Selection Events
  bindSelectionEvents() {
    // Product checkboxes
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('product-checkbox-input')) {
        this.handleProductSelection(e.target);
      }
      
      if (e.target.classList.contains('variant-checkbox-input')) {
        this.handleVariantSelection(e.target);
      }
    });

    // Product card clicks
    document.addEventListener('click', (e) => {
      if (e.target.closest('.modern-product-card')) {
        const card = e.target.closest('.modern-product-card');
        const checkbox = card.querySelector('.product-checkbox-input');
        
        // Only toggle if clicking on the card itself, not on buttons or checkboxes
        if (!e.target.closest('.product-actions') && 
            !e.target.closest('.product-checkbox-container') &&
            !e.target.closest('.variant-checkbox')) {
          if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.handleProductSelection(checkbox);
          }
        }
      }
    });

    // Bulk actions
    const bulkActionBtns = document.querySelectorAll('.bulk-action-btn');
    bulkActionBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        this.handleBulkAction(btn.dataset.action);
      });
    });
  }

  // Keyboard Shortcuts
  bindKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+K or Cmd+K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
      }

      // Escape to clear search
      if (e.key === 'Escape') {
        const searchInput = document.querySelector('.search-input');
        if (searchInput && document.activeElement === searchInput) {
          searchInput.value = '';
          this.performSearch('');
        }
      }

      // Ctrl+A to select all visible products
      if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.closest('.products-list')) {
        e.preventDefault();
        this.selectAllVisible();
      }
    });
  }

  // Search Implementation
  performSearch(query) {
    console.log('🔍 Performing search:', query);
    
    const form = document.querySelector('#product-search-form');
    if (form) {
      const searchInput = form.querySelector('input[name="search"]');
      if (searchInput) {
        searchInput.value = query;
      }
      
      // Show loading state
      this.showSearchLoading();
      
      // Submit form (Turbo will handle the response)
      form.requestSubmit();
    }
  }

  // Filter Implementation
  applyFilters() {
    console.log('🔧 Applying filters');
    
    const form = document.querySelector('#product-search-form');
    if (form) {
      this.showSearchLoading();
      form.requestSubmit();
    }
  }

  clearAllFilters() {
    console.log('🗑️ Clearing all filters');
    
    const form = document.querySelector('#product-search-form');
    if (form) {
      // Reset all form fields
      const searchInput = form.querySelector('input[name="search"]');
      const statusSelect = form.querySelector('select[name="status"]');
      const sortBySelect = form.querySelector('select[name="sort_by"]');
      const sortOrderSelect = form.querySelector('select[name="sort_order"]');
      const pageInput = form.querySelector('input[name="page"]');

      if (searchInput) searchInput.value = '';
      if (statusSelect) statusSelect.value = 'active';
      if (sortBySelect) sortBySelect.value = 'name';
      if (sortOrderSelect) sortOrderSelect.value = 'asc';
      if (pageInput) pageInput.value = 1;

      // Clear quick filters
      document.querySelectorAll('.quick-filter-btn.active').forEach(btn => {
        btn.classList.remove('active');
      });

      this.showSearchLoading();
      form.requestSubmit();
    }
  }

  toggleQuickFilter(btn) {
    btn.classList.toggle('active');
    
    // Apply the quick filter logic here
    const filterType = btn.dataset.filter;
    const filterValue = btn.dataset.value;
    
    console.log('⚡ Quick filter:', filterType, filterValue);
    
    // Update form and submit
    this.applyFilters();
  }

  // Selection Implementation
  handleProductSelection(checkbox) {
    const productId = checkbox.dataset.productId;
    const card = checkbox.closest('.modern-product-card');
    
    if (checkbox.checked) {
      this.selectedProducts.add(productId);
      card.classList.add('selected');
      card.classList.add('selecting');
      
      // Remove animation class after animation completes
      setTimeout(() => {
        card.classList.remove('selecting');
      }, 300);
      
      console.log('✅ Selected product:', productId);
    } else {
      this.selectedProducts.delete(productId);
      card.classList.remove('selected');
      console.log('❌ Deselected product:', productId);
    }
    
    this.updateBulkActions();
    this.updateSelectionStats();
  }

  handleVariantSelection(checkbox) {
    const variantId = checkbox.dataset.variantId;
    const variantItem = checkbox.closest('.variant-item');
    
    if (checkbox.checked) {
      this.selectedVariants.add(variantId);
      variantItem.classList.add('selected');
      console.log('✅ Selected variant:', variantId);
    } else {
      this.selectedVariants.delete(variantId);
      variantItem.classList.remove('selected');
      console.log('❌ Deselected variant:', variantId);
    }
    
    this.updateBulkActions();
    this.updateSelectionStats();
  }

  selectAllVisible() {
    const visibleCheckboxes = document.querySelectorAll('.modern-product-card:not([style*="display: none"]) .product-checkbox-input');
    
    visibleCheckboxes.forEach(checkbox => {
      if (!checkbox.checked) {
        checkbox.checked = true;
        this.handleProductSelection(checkbox);
      }
    });
    
    console.log('📦 Selected all visible products');
  }

  // Bulk Actions
  handleBulkAction(action) {
    const selectedCount = this.selectedProducts.size + this.selectedVariants.size;
    
    if (selectedCount === 0) {
      alert('Please select products or variants first.');
      return;
    }
    
    console.log(`🔄 Bulk action: ${action} on ${selectedCount} items`);
    
    switch (action) {
      case 'assign':
        this.bulkAssignProducts();
        break;
      case 'remove':
        this.bulkRemoveProducts();
        break;
      case 'export':
        this.bulkExportProducts();
        break;
      default:
        console.warn('Unknown bulk action:', action);
    }
  }

  bulkAssignProducts() {
    const productIds = Array.from(this.selectedProducts);
    const variantIds = Array.from(this.selectedVariants);
    
    console.log('📦 Bulk assigning:', { productIds, variantIds });
    
    // Implementation would depend on your backend API
    // This is a placeholder for the actual implementation
    alert(`Assigning ${productIds.length} products and ${variantIds.length} variants...`);
  }

  bulkRemoveProducts() {
    if (confirm('Are you sure you want to remove the selected items?')) {
      console.log('🗑️ Bulk removing selected items');
      // Implementation here
    }
  }

  bulkExportProducts() {
    console.log('📤 Bulk exporting selected items');
    // Implementation here
  }

  // UI Updates
  updateBulkActions() {
    const selectedCount = this.selectedProducts.size + this.selectedVariants.size;
    const bulkActionBtns = document.querySelectorAll('.bulk-action-btn');
    
    bulkActionBtns.forEach(btn => {
      btn.disabled = selectedCount === 0;
    });
  }

  updateSelectionStats() {
    const selectedCount = this.selectedProducts.size + this.selectedVariants.size;
    const statsElement = document.querySelector('.selection-stats');
    
    if (statsElement) {
      statsElement.textContent = `${selectedCount} selected`;
      statsElement.style.display = selectedCount > 0 ? 'block' : 'none';
    }
  }

  setupViewToggle() {
    const viewToggleBtns = document.querySelectorAll('.view-toggle-btn');
    
    viewToggleBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const view = btn.dataset.view;
        this.switchView(view);
        
        // Update active state
        viewToggleBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
      });
    });
  }

  switchView(view) {
    this.currentView = view;
    const productsContainer = document.querySelector('.products-list');
    
    if (productsContainer) {
      productsContainer.classList.remove('grid-view', 'list-view');
      productsContainer.classList.add(`${view}-view`);
    }
    
    console.log('👁️ Switched to view:', view);
  }

  // Loading States
  showSearchLoading() {
    const resultsContainer = document.querySelector('.products-list');
    if (resultsContainer) {
      resultsContainer.style.opacity = '0.6';
      resultsContainer.style.pointerEvents = 'none';
    }
  }

  hideSearchLoading() {
    const resultsContainer = document.querySelector('.products-list');
    if (resultsContainer) {
      resultsContainer.style.opacity = '1';
      resultsContainer.style.pointerEvents = 'auto';
    }
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (document.querySelector('.enhanced-search-container') || 
      document.querySelector('.modern-product-card')) {
    new EnhancedProductSearch();
  }
});

// Handle Turbo navigation
document.addEventListener('turbo:load', () => {
  if (document.querySelector('.enhanced-search-container') || 
      document.querySelector('.modern-product-card')) {
    new EnhancedProductSearch();
  }
});

// Export for use in other modules
window.EnhancedProductSearch = EnhancedProductSearch;
