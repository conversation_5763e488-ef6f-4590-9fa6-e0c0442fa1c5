<div class="step-content" id="step-2">
  <form data-controller="category-builder"
        data-category-builder-categories-value="<%= (@wizard&.categories || []).to_json %>"
        data-action="submit->category-builder#saveCategories">

    <div class="row">
      <div class="col-12">
        <div class="mb-4">
          <h4 class="mb-1">📂 Bundle Categories</h4>
          <p class="text-muted mb-0">Configure product categories for your bundle</p>
        </div>

        <div class="row">
          <div class="col-12">
            <!-- Categories Container -->
            <div data-category-builder-target="categoriesContainer">
              <!-- Categories will be added here -->
              <div data-category-builder-target="categoriesList">
                <!-- Dynamic categories -->
              </div>
            </div>
          </div>
        </div>

        <!-- Add Category Button (always visible at bottom, outside categories container) -->
        <div class="mt-4 text-center">
          <button type="button"
                  class="btn btn-primary"
                  data-action="click->category-builder#addCategory"
                  data-category-builder-target="addCategoryButton">
            + Add Category
          </button>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="d-flex justify-content-between mt-5">
      <button type="button" class="btn btn-outline-secondary" onclick="previousStep()">
        ← Back to Bundle Info
      </button>
      <div>
        <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
          Save Draft
        </button>
        <button type="button" class="btn btn-primary" onclick="nextStep()" id="continueToPreview">
          Continue to Preview →
        </button>
      </div>
    </div>

  </form>

</div>

<!-- Product Selection Modal -->
<div class="modal fade" id="productSelectionModal" tabindex="-1" aria-labelledby="productSelectionModalLabel" aria-hidden="true"
     data-controller="product-search"
     data-product-search-search-url-value="/admin/products/search">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">

      <!-- Modern Product Cards Styles for Modal -->
      <style>
        .modal-body .modern-product-card {
          background: white;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          padding: 8px;
          margin-bottom: 8px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .modal-body .modern-product-card:hover {
          border-color: #007bff;
          box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
          transform: translateY(-1px);
        }

        .modal-body .modern-product-card.selected {
          border-color: #28a745;
          background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
          box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
        }

        /* Single Line Layout */
        .modal-body .product-main-line {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .modal-body .product-image-container {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          overflow: hidden;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          border: 1px solid #e9ecef;
        }

        .modal-body .product-image-placeholder {
          font-size: 14px;
          color: #6c757d;
          opacity: 0.7;
        }

        .modal-body .product-info {
          flex: 1;
          min-width: 0;
        }

        .modal-body .product-title {
          font-size: 13px;
          font-weight: 600;
          color: #212529;
          margin: 0;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .modal-body .product-status-price {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }

        .modal-body .product-price {
          color: #28a745;
          font-weight: 600;
          font-size: 12px;
        }

        .modal-body .status-badge.in-stock {
          background: #d4edda;
          color: #155724;
          padding: 1px 4px;
          border-radius: 8px;
          font-size: 9px;
          font-weight: 600;
          text-transform: uppercase;
        }

        /* Inline Variants Button */
        .modal-body .variants-inline-btn {
          background: none;
          border: none;
          padding: 2px 4px;
          border-radius: 4px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 3px;
          color: #6c757d;
          transition: all 0.2s ease;
          font-size: 9px;
          margin-left: 4px;
        }

        .modal-body .variants-inline-btn:hover {
          background: #f8f9fa;
          color: #007bff;
        }

        .modal-body .variants-inline-btn.expanded {
          color: #007bff;
          background: #e3f2fd;
        }

        .modal-body .variants-inline-btn .variants-label {
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .modal-body .variants-inline-btn .chevron-icon {
          font-size: 8px;
          transition: transform 0.2s ease;
          display: inline-block;
        }





        .modal-body .modern-checkbox {
          width: 16px;
          height: 16px;
          border: 2px solid #ced4da;
          border-radius: 3px;
          background: white;
          cursor: pointer;
          position: relative;
          transition: all 0.2s ease;
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
        }

        .modal-body .modern-checkbox:checked {
          background: #007bff;
          border-color: #007bff;
        }

        .modal-body .modern-checkbox:checked::after {
          content: '✓';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 10px;
          font-weight: bold;
        }

        .modal-body .modern-checkbox:hover {
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }



        .modal-body .product-variants {
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #e9ecef;
        }

        .modal-body .variants-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
        }

        .modal-body .variants-title {
          font-size: 12px;
          font-weight: 600;
          color: #495057;
          margin: 0;
        }

        .modal-body .variants-count {
          background: #e9ecef;
          color: #495057;
          padding: 1px 6px;
          border-radius: 8px;
          font-size: 9px;
          font-weight: 600;
        }

        .modal-body .variant-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 6px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 3px;
          margin-bottom: 4px;
          transition: all 0.2s ease;
        }

        .modal-body .variant-item:hover {
          background: #e3f2fd;
          border-color: #007bff;
        }

        .modal-body .variant-item.selected {
          background: #d4edda;
          border-color: #28a745;
        }

        .modal-body .variant-title {
          font-size: 11px;
          font-weight: 500;
          color: #212529;
        }

        .modal-body .variant-details {
          display: flex;
          gap: 8px;
          align-items: center;
          font-size: 9px;
          color: #6c757d;
        }

        .modal-body .variant-price {
          color: #28a745;
          font-weight: 600;
        }

        .modal-body .variant-checkbox-input {
          width: 14px;
          height: 14px;
          border: 2px solid #ced4da;
          border-radius: 2px;
          background: white;
          cursor: pointer;
          position: relative;
          transition: all 0.2s ease;
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
        }

        .modal-body .variant-checkbox-input:checked {
          background: #007bff;
          border-color: #007bff;
        }

        .modal-body .variant-checkbox-input:checked::after {
          content: '✓';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 8px;
          font-weight: bold;
        }

        .modal-body .variant-checkbox-input:hover {
          border-color: #007bff;
          box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.1);
        }

        /* Custom Scrollbar */
        .products-scroll-area {
          scrollbar-width: thin;
          scrollbar-color: #ced4da #f8f9fa;
        }

        .products-scroll-area::-webkit-scrollbar {
          width: 6px;
        }

        .products-scroll-area::-webkit-scrollbar-track {
          background: #f8f9fa;
          border-radius: 3px;
        }

        .products-scroll-area::-webkit-scrollbar-thumb {
          background: #ced4da;
          border-radius: 3px;
          transition: background 0.2s ease;
        }

        .products-scroll-area::-webkit-scrollbar-thumb:hover {
          background: #007bff;
        }
      </style>
      <div class="modal-header">
        <h5 class="modal-title" id="productSelectionModalLabel">Add Products to Category</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <div class="modal-body">
        <!-- Search Input (Fixed at top) -->
        <div class="search-section mb-3" style="position: sticky; top: 0; background: white; z-index: 10; padding-bottom: 10px; border-bottom: 1px solid #e9ecef;">
          <label for="productSearch" class="form-label">Search Products</label>
          <input type="text"
                 class="form-control"
                 data-product-search-target="searchInput"
                 data-action="input->product-search#searchProducts"
                 placeholder="Type at least 2 characters to search...">

          <!-- Search Help -->
          <div class="form-text mt-2">
            <small class="text-muted">
              <strong>Search in:</strong> Product name, SKU, description, and variants
              <br>
              <strong>Examples:</strong> "protein", "SUPP-001", "weight loss"
            </small>
          </div>
        </div>

        <!-- Scrollable Products Area -->
        <div class="products-scroll-area" style="max-height: 50vh; overflow-y: auto; padding-right: 5px;">
          <!-- Loading State -->
          <div class="text-center py-4 hidden" data-product-search-target="productsLoading">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">Loading products...</div>
          </div>

          <!-- Products Container -->
          <div data-product-search-target="productsContainer">
            <!-- Products List (populated dynamically) -->
            <div data-product-search-target="productsList">
              <!-- Products will be loaded here dynamically -->
            </div>

            <!-- Empty State -->
            <div class="text-center py-5 hidden" data-product-search-target="productsEmpty">
              <div class="text-muted">
                <div style="font-size: 3rem;">🔍</div>
                <h6 class="mt-3 mb-2">No products found</h6>
                <p class="mb-3 small">Try a different search term or check your spelling</p>
                <div class="mt-3">
                  <small class="text-muted">
                    <strong>Search tips:</strong><br>
                    • Use product names: "protein", "vitamins"<br>
                    • Use SKUs: "SUPP-001", "VIT-B12"<br>
                    • Use descriptions: "weight loss", "energy"<br>
                    • Check variant names and SKUs
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer d-flex justify-content-between align-items-center">
        <div class="selection-info">
          <span class="text-muted" id="selection-counter">
            <span class="badge bg-secondary">0 selected</span>
          </span>
        </div>

        <div class="modal-actions">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button"
                  class="btn btn-primary"
                  data-action="click->product-search#confirmSelection"
                  id="add-selected-btn"
                  disabled>
            <i class="fas fa-plus me-1"></i>
            Add Selected Products
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Category Builder Interface Layout */
.category-builder-interface {
  height: calc(100vh - 210px);
  min-height: 500px;
}

/* Category Cards */
.category-card {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.category-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Products Drop Zone */
.products-drop-zone {
  min-height: 100px;
  transition: all 0.3s ease;
}

.products-drop-zone.border-blue-500 {
  border-color: #3b82f6 !important;
  background-color: #eff6ff !important;
}

/* Product Cards */
.product-card {
  transition: all 0.2s ease;
  cursor: move;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-card.opacity-50 {
  opacity: 0.5;
}

/* Product in Category */
.product-in-category {
  transition: all 0.2s ease;
}

.product-in-category:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Hidden utility */
.hidden {
  display: none !important;
}
</style>
